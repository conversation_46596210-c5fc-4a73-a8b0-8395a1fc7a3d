FROM registry.cn-hangzhou.aliyuncs.com/guoshu/golang:1.23-alpine3.20 AS builder
WORKDIR /app
COPY . .
ENV GOPROXY https://goproxy.cn
RUN go mod tidy
RUN CGO_ENABLED=0 GO111MODULE=on GOARCH=amd64 GOOS=linux
RUN go build -o main main.go
# go build -tags=jsoniter .

FROM registry.cn-hangzhou.aliyuncs.com/guoshu/alpine:3.20
WORKDIR /app
COPY --from=builder /app/main /app/main
COPY --from=builder /app/core/files /app/core/files
COPY --from=builder /app/config.yaml /app/config.yaml

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

RUN apk update && apk add tzdata
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone

#ENV TZ Asia/Shanghai
#
#RUN apk add alpine-conf && \
#    /sbin/setup-timezone -z Asia/Shanghai && \
#    apk del alpine-conf
ENV PATH="/app:${PATH}"

EXPOSE 11001

CMD ["./main"]