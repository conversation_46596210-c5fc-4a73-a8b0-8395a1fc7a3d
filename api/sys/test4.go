package sys

import (
	"base/core/xhttp"
	"base/dao"
	"base/dao/cartDao"
	"base/dao/orderDao"
	"base/dao/orderRefundDao"
	"base/dao/productBuyPriceDao"
	"base/dao/productDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/service/orderFinalSettleService"
	"base/util"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

// Test4 测试
func Test4(ctx *gin.Context) {
	var req = struct {
		P1 string `json:"p1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// addDefaultSkuForProducts(ctx)
	// dealCartData(ctx)
	// BatchUpdateSkuStock(ctx)

	monthList := []int{
		// 12,
		// 11,
		// 10,
		// 9,
		// 8,
		// 7,
		// 6,
		// 5,
		// 4,
		// 3,
		// 2,
		// 1,
	}
	// 获取月份的开始和结束时间-毫秒
	for _, month := range monthList {
		startTime := time.Date(2025, time.Month(month), 1, 0, 0, 0, 0, time.Local)
		endTime := startTime.AddDate(0, 1, 0)
		startMillis := startTime.UnixMilli()
		endMillis := endTime.UnixMilli()
		fmt.Printf("月份: %d, 开始时间(毫秒): %d, 结束时间(毫秒): %d\n", month, startMillis, endMillis)

		// completeOrderSkuFields(ctx, month, startMillis, endMillis)
		// completeSettleOrderSkuFields(ctx, month, startMillis, endMillis)
		// completeRefundOrderSkuFields(ctx, month, startMillis, endMillis)
		// completeQualityOrderSkuFields(ctx, month, startMillis, endMillis)
		// syncProductBuyPriceSkuName(ctx, month, startMillis, endMillis)
		// dealOrderSnapshot(ctx, month, startMillis, endMillis)

	}

	// updateProductStockTo100(ctx)
	// updateStartWeightUnitPrice(ctx)
	// checkProductTitleAndDesc(ctx)
	// checkProductUserTypeEmpty(ctx)

	// 为商品标签添加时间字段
	// updateProductTagTimeFields(ctx)

	// messageID := "eyJvcmRlcl9pZF9saXN0IjpbIjY4NzBiYmRmZjc0ZDdiNjg1ODIwMmMzYyJdfQ=="

	// var data model.MNSOrderList
	// util.DecodeMNSInfo("aaa", messageID, &data)

	// fmt.Println(data)

	// CheckProductUpdatedAtDuplicates(ctx)
	// CheckPartProductSale(ctx)

	// checkRefundComplete(ctx)

	// orderID, _ := primitive.ObjectIDFromHex(req.P1)

	// err = orderFinalSettleService.NewService().CreateFromOrder(ctx, orderID)
	// if err != nil {
	// 	zap.S().Errorf("创建最终结算记录失败: %v", err.Error())
	// 	return
	// }

	// BatchUpdateSkuStock(ctx)

	// batchSendDeliverFeeDetailGenerate(ctx, 1748744883000)
	// listSupplierInfo(ctx)

	// 查询状态不可用的供应商，并打印供应商信息
	// suppliers := queryUnavailableSuppliers(ctx)

	// // 设置查询月份（当前月份）
	// monthTimestamp := time.Now().UnixMilli()
	// monthStart, monthEnd, err := util.MonthScopeTimestamp(monthTimestamp)
	// if err != nil {
	// 	zap.S().Errorf("获取月份时间范围失败: %v", err)
	// 	return
	// }

	// // 创建输出文件
	// monthStr := time.Unix(monthStart/1000, 0).Format("2006年01月")
	// fileName := fmt.Sprintf("supplier_stats_%s_%d.txt", monthStr, time.Now().Unix())
	// file, err := os.Create(fileName)
	// if err != nil {
	// 	zap.S().Errorf("创建文件失败: %v", err)
	// 	return
	// }
	// defer file.Close()

	// // 写入文件头部
	// header := fmt.Sprintf("供应商统计信息（%s）\n生成时间: %s\n统计时间范围: %s 至 %s\n\n",
	// 	monthStr,
	// 	time.Now().Format("2006-01-02 15:04:05"),
	// 	time.Unix(monthStart/1000, 0).Format("2006-01-02 15:04:05"),
	// 	time.Unix(monthEnd/1000, 0).Format("2006-01-02 15:04:05"))
	// file.WriteString(header)

	// // 排除的供应商id列表
	// excludeIDs := []string{
	// 	"64560329f99212289203a47d", "64560329f99212289203a47f", "6456032af99212289203a483",
	// 	"6456032bf99212289203a489", "6456032cf99212289203a48b", "6456032df99212289203a491",
	// 	"6456032ef99212289203a495", "646ef189853c3a62068cf2d9", "64e412d305ca387cc1c8b8aa",
	// 	"65605408f110a8bee4fa7bfe", "67caba2213fe2efb5af8a79e", "66544f3bfd492ccde47cf59d", "66b337aca9fb5e25a3587cbc"}
	// _ = excludeIDs

	// var emptyList []primitive.ObjectID

	// for _, supplier := range suppliers {
	// 	var f bool
	// 	for _, excludeID := range excludeIDs {
	// 		if supplier.ID.Hex() == excludeID {
	// 			f = true
	// 		}
	// 	}
	// 	if f {
	// 		continue
	// 	}
	// 	count := queryLatestOrdersBySupplierID(ctx, supplier, file)

	// 	if count == 0 {
	// 		emptyList = append(emptyList, supplier.ID)
	// 	}
	// }

	// marshalJSON, _ := json.Marshal(emptyList)
	// zap.S().Infof("统计完成，结果已保存到文件: %s", fileName)
	// zap.S().Infof("emptyList: %v", string(marshalJSON))

	//checkOrderSettle(ctx)

	// 重置供应商冻结金额为0
	// resetSupplierFrozenBalanceAmount(ctx)

	//refreshTotalProfit(ctx)
	printOrderFinalSettle(ctx)

}

func checkOrderSettle(ctx context.Context) {
	orderDaoInstance := orderDao.NewOrderDao("order")

	//id, _ := primitive.ObjectIDFromHex("6481ef7527b29d1c3e17c056")
	//id, _ := primitive.ObjectIDFromHex("6456032df99212289203a48f") // 中云
	//id, _ := primitive.ObjectIDFromHex("6456032ff99212289203a499") // 云果地
	//id, _ := primitive.ObjectIDFromHex("64560330f99212289203a49d") // 甜甜
	id, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b529f") // 优品
	//oid, _ := primitive.ObjectIDFromHex("68676798f3f9190ba8615e4a")

	// 查找所有订单
	filter := bson.M{
		"supplier_id": id,
		//"_id":         oid,
		"has_agent_pay": true,
		"created_at": bson.M{
			"$gte": 1753977600000, // 2025-08-01
			"$lte": 1754841600000, // 2025-08-11
		},
	}

	orders, err := orderDaoInstance.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询订单失败: %v", err.Error())
		return
	}

	for _, order := range orders {
		mnsSendService.NewMNSClient().SendOrderFinalSettle(ctx, order.ID)
	}

}

func printOrderFinalSettle(ctx context.Context) {
	//id, _ := primitive.ObjectIDFromHex("6481ef7527b29d1c3e17c056")
	//id, _ := primitive.ObjectIDFromHex("6456032df99212289203a48f") // 中云
	//id, _ := primitive.ObjectIDFromHex("6456032ff99212289203a499") // 云果地
	//id, _ := primitive.ObjectIDFromHex("64560330f99212289203a49d") // 甜甜
	id, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b529f") // 优品
	//oid, _ := primitive.ObjectIDFromHex("68676798f3f9190ba8615e4a")

	// 查找所有订单
	filter := bson.M{
		"supplier_id":     id,
		"month_timestamp": 1753977600000, // 08-01
	}
	list, err := orderFinalSettleService.NewService().List(ctx, filter)
	if err != nil {
		return
	}

	for _, settle := range list {

	}

}

func refreshTotalProfit(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b529f") // 优品

	orderFinalSettleService.NewService().RefreshTotalByMonth(ctx, id, 1753977600000)

}

// 检查商品的user_type是否为空字符串
func checkProductUserTypeEmpty(ctx context.Context) {
	productDaoInstance := productDao.NewProductDao("product")
	rdsInstance := global.RDBDefault

	filter := bson.M{}
	page := int64(1)
	limit := int64(500)

	for {
		products, _, err := productDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询user_type为空的商品失败: %v", err.Error())
			return
		}
		if len(products) == 0 {
			break
		}

		var needUpdateID []primitive.ObjectID
		for _, product := range products {
			if product.UserType == "" {
				zap.S().Infof("商品ID: %s user_type为空字符串", product.ID.Hex())
				needUpdateID = append(needUpdateID, product.ID)
			}
		}

		if len(needUpdateID) > 0 {
			err = productDaoInstance.UpdateMany(ctx, bson.M{"_id": bson.M{"$in": needUpdateID}}, bson.M{"$set": bson.M{"user_type": model.UserTypeNormal}})
			if err != nil {
				zap.S().Errorf("更新user_type失败: %v", err.Error())
				return
			}

			for _, id := range needUpdateID {
				redisKey := fmt.Sprintf("product:%s", id.Hex())
				rdsInstance.Del(ctx, redisKey)
			}
		}
		zap.S().Infof("页码：%d ", page)

		page++
	}
}

// 检查商品标题，描述是否存在换行符和空格
func checkProductTitleAndDesc(ctx context.Context) {
	productDaoInstance := productDao.NewProductDao("product")
	rdsInstance := global.RDBDefault

	filter := bson.M{}
	page := int64(1)
	limit := int64(100)

	for {
		products, _, err := productDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询商品失败: %v", err.Error())
			return
		}
		if len(products) == 0 {
			break
		}

		for index, product := range products {
			update := bson.M{}

			var f bool
			if isExistWrap(product.Title) {
				zap.S().Infof("商品 %s 标题存在换行符和空格", product.ID.Hex())
				f = true
				update["title"] = util.DealWrap(product.Title)
			}

			if isExistWrap(product.Desc) {
				zap.S().Infof("商品 %s 描述存在换行符和空格", product.ID.Hex())
				f = true
				update["desc"] = util.DealWrap(product.Desc)
			}

			if f {
				zap.S().Infof("商品 %s 标题或描述存在换行符和空格", product.ID.Hex())
				zap.S().Infof("页码：%d,进度：%d/%d", page, index+1, len(products))
			}

			if f {
				err = productDaoInstance.Update(ctx, bson.M{"_id": product.ID}, bson.M{"$set": update})
				if err != nil {
					zap.S().Errorf("更新商品 %s 失败: %v", product.ID.Hex(), err)
					continue
				}
				redisKey := fmt.Sprintf("product:%s", product.ID.Hex())
				rdsInstance.Del(ctx, redisKey)
			}
		}
		page++
	}
}

// 判断函数，是否存在换行符和空格
func isExistWrap(str string) bool {
	return strings.Contains(str, "\n") || strings.Contains(str, " ")
}

// 更新商品起售重量单价
func updateStartWeightUnitPrice(ctx context.Context) {
	productDaoInstance := productDao.NewProductDao("product")
	rdsInstance := global.RDBDefault

	id, _ := primitive.ObjectIDFromHex("645f288c3a708b5a31b7bf0b")
	_ = id

	filter := bson.M{
		"is_check_weight": true,
	}
	page := int64(1)
	limit := int64(100)

	for {
		products, _, err := productDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询商品失败: %v", err.Error())
			return
		}
		if len(products) == 0 {
			break
		}

		for index, product := range products {
			// 使用正确的字段名称：Weight.RoughWeight和Price

			if product.StartWeightUnitPrice > 0 {
				continue
			}

			startPrice := product.StartPrice

			weight := 0
			for _, v := range product.SkuList {
				if v.Price == startPrice {
					weight = v.RoughWeight
					break
				}
			}

			if weight == 0 {
				zap.S().Infof("商品 %s 没有找到起售重量单价", product.ID.Hex())
				continue
			}

			startWeightUnitPrice := dealStartWeightUnitPrice(ctx, weight, startPrice)
			update := bson.M{
				"$set": bson.M{
					"start_weight_unit_price": startWeightUnitPrice,
				},
			}
			err = productDaoInstance.Update(ctx, bson.M{"_id": product.ID}, update)
			if err != nil {
				zap.S().Errorf("更新商品 %s 失败: %v", product.ID.Hex(), err)
				continue
			}
			// 移除缓存
			redisKey := fmt.Sprintf("product:%s", product.ID.Hex())
			rdsInstance.Del(ctx, redisKey)

			zap.S().Infof("页码：%d,进度：%d/%d", page, index+1, len(products))
		}
		page++
	}
}

func dealStartWeightUnitPrice(ctx context.Context, minRoughWeight int, minPrice int) int {
	_ = ctx

	startWeightUnitPrice := 0
	// 使用decimal计算
	minRoughWeightDecimal := decimal.NewFromInt(int64(minRoughWeight)).Div(decimal.NewFromInt(1000)) // kg
	minPriceDecimal := decimal.NewFromInt(int64(minPrice))

	startWeightUnitPriceDecimal := minPriceDecimal.Div(minRoughWeightDecimal)
	// 乘以10后四舍五入，再转为int，确保精确到角
	startWeightUnitPrice = int(startWeightUnitPriceDecimal.Div(decimal.NewFromInt(10)).Round(0).IntPart() * 10)
	return startWeightUnitPrice
}

// 处理商品库存，将所有库存为0的商品库存更新为100
func updateProductStockTo100(ctx context.Context) {
	productDaoInstance := productDao.NewProductDao("product")
	rdsInstance := global.RDBDefault

	filter := bson.M{
		"stock": 0,
	}

	page := int64(1)
	limit := int64(50)
	totalUpdated := 0

	for {
		products, _, err := productDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询库存为0的商品失败: %v", err.Error())
			return
		}
		if len(products) == 0 {
			break
		}

		updated := 0
		for _, product := range products {
			update := bson.M{
				"$set": bson.M{
					"stock": 100,
				},
			}
			err := productDaoInstance.Update(ctx, bson.M{"_id": product.ID}, update)
			if err != nil {
				fmt.Printf("更新商品 %s 库存失败: %v\n", product.ID.Hex(), err)
				continue
			}
			redisKey := fmt.Sprintf("product:%s", product.ID.Hex())
			rdsInstance.Del(ctx, redisKey)
			updated++
		}
		totalUpdated += updated
		zap.S().Infof("第 %d 页处理完成，更新了 %d 个商品库存，总计已更新 %d 个商品", page, updated, totalUpdated)
		page++
	}
}

// completeOrderSkuFields 补全订单商品列表的sku字段
func completeOrderSkuFields(ctx context.Context, month int, beginTime, endTime int64) {
	orderDaoInstance := orderDao.NewOrderDao("order")
	productDaoInstance := productDao.NewProductDao("product")

	// 查找所有订单
	filter := bson.M{
		"created_at": bson.M{
			"$gte": beginTime,
			"$lte": endTime,
		},
	}

	orders, err := orderDaoInstance.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询订单失败: %v", err.Error())
		return
	}

	updatedOrderCount := 0

	for index, order := range orders {
		orderUpdated := false

		pList := order.ProductList

		for i, productOrder := range pList {
			// 检查是否缺少sku字段
			if productOrder.SkuIDCode == "" || productOrder.SkuName == "" {
				// 查询商品信息
				product, err := productDaoInstance.Get(ctx, bson.M{"_id": productOrder.ProductID})
				if err != nil {
					fmt.Printf("查询商品 %s 失败: %v\n", productOrder.ProductID.Hex(), err)
					continue
				}

				// 如果商品有SKU列表，使用第一个SKU
				sku := product.SkuList[0]
				pList[i].SkuIDCode = sku.IDCode
				pList[i].SkuName = sku.Name
				orderUpdated = true
			}
		}

		// 如果订单有更新，保存到数据库
		if orderUpdated {
			update := bson.M{
				"$set": bson.M{
					"product_list": pList,
				},
			}

			err = orderDaoInstance.UpdateOne(ctx, bson.M{"_id": order.ID}, update)
			if err != nil {
				fmt.Printf("更新订单 %s 失败: %v\n", order.ID.Hex(), err)
				continue
			}

			updatedOrderCount++
			zap.S().Infof("更新订单，进度：%d/%d", index+1, len(orders))
		}

	}

	zap.S().Infof("成功更新 %d 个订单，月份：%d", updatedOrderCount, month)
}

// 结算订单
func completeSettleOrderSkuFields(ctx context.Context, month int, beginTime, endTime int64) {
	orderDebtDaoInstance := dao.OrderDebtDao
	orderDaoInstance := orderDao.NewOrderDao("order")

	// 查找所有结算订单
	filter := bson.M{
		"created_at": bson.M{
			"$gte": beginTime,
			"$lte": endTime,
		},
	}

	settleOrders, err := orderDebtDaoInstance.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询结算订单失败: %v", err.Error())
		return
	}

	updatedOrderCount := 0

	for index, settleOrder := range settleOrders {
		orderUpdated := false

		var f bool
		for _, productSettle := range settleOrder.SettleProductList {
			if productSettle.SkuIDCode == "" {
				f = true
			}
		}

		if !f {
			continue
		}

		order, err := orderDaoInstance.Get(ctx, bson.M{"_id": settleOrder.OrderID})
		if err != nil {
			zap.S().Errorf("查询订单 %s 失败: %v", settleOrder.OrderID.Hex(), err)
			continue
		}

		// 处理SettleProductList
		for i, productSettle := range settleOrder.SettleProductList {
			if productSettle.SkuIDCode == "" {
				// 查询商品信息
				skuIDCode := ""
				skuName := ""
				for _, v := range order.ProductList {
					if v.ProductID == productSettle.ProductID {
						skuIDCode = v.SkuIDCode
						skuName = v.SkuName
					}
				}
				settleOrder.SettleProductList[i].SkuIDCode = skuIDCode
				settleOrder.SettleProductList[i].SkuName = skuName
				orderUpdated = true
			}
		}

		// 如果订单有更新，保存到数据库
		if orderUpdated {
			update := bson.M{
				"$set": bson.M{
					"settle_product_list": settleOrder.SettleProductList,
				},
			}

			err = orderDebtDaoInstance.UpdateOne(ctx, bson.M{"_id": settleOrder.ID}, update)
			if err != nil {
				zap.S().Errorf("更新结算订单 %s 失败: %v", settleOrder.ID.Hex(), err)
				continue
			}

			updatedOrderCount++
			zap.S().Infof("月份：%d，更新结算订单，进度：%d/%d", month, index+1, len(settleOrders))

		}

	}

	zap.S().Infof("成功更新 %d 个结算订单，月份：%d", updatedOrderCount, month)
}

// 售后
func completeRefundOrderSkuFields(ctx context.Context, month int, beginTime, endTime int64) {
	orderRefundDaoInstance := dao.OrderRefundDao
	orderDaoInstance := orderDao.NewOrderDao("order")

	// 查找所有退款订单
	filter := bson.M{
		"created_at": bson.M{
			"$gte": beginTime,
			"$lte": endTime,
		},
	}

	refundOrders, err := orderRefundDaoInstance.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询退款订单失败: %v", err.Error())
		return
	}

	updatedOrderCount := 0

	for index, refundOrder := range refundOrders {
		orderUpdated := false

		if refundOrder.SkuIDCode != "" {
			continue
		}

		order, err := orderDaoInstance.Get(ctx, bson.M{"_id": refundOrder.OrderID})
		if err != nil {
			zap.S().Errorf("查询订单 %s 失败: %v", refundOrder.OrderID.Hex(), err)
			continue
		}

		// 检查退款订单是否缺少sku_id_code
		if refundOrder.SkuIDCode == "" {
			// 查询商品信息
			skuIDCode := ""
			skuName := ""
			for _, v := range order.ProductList {
				if v.ProductID == refundOrder.ProductID {
					skuIDCode = v.SkuIDCode
					skuName = v.SkuName
				}
			}

			// 如果商品有SKU列表，使用第一个SKU
			refundOrder.SkuIDCode = skuIDCode
			refundOrder.SkuName = skuName
			orderUpdated = true
			zap.S().Infof("更新退款订单商品 %s，SKU: %s", refundOrder.ProductID.Hex(), skuIDCode)
		}

		// 如果订单有更新，保存到数据库
		if orderUpdated {
			update := bson.M{
				"$set": bson.M{
					"sku_id_code": refundOrder.SkuIDCode,
					"sku_name":    refundOrder.SkuName,
				},
			}

			err = orderRefundDaoInstance.UpdateOne(ctx, bson.M{"_id": refundOrder.ID}, update)
			if err != nil {
				zap.S().Errorf("更新退款订单 %s 失败: %v", refundOrder.ID.Hex(), err)
				continue
			}

			updatedOrderCount++
			zap.S().Infof("月份：%d，更新退款订单，进度：%d/%d", month, index+1, len(refundOrders))
		}
	}

	zap.S().Infof("成功更新 %d 个退款订单，月份：%d", updatedOrderCount, month)
}

// 品控单
func completeQualityOrderSkuFields(ctx context.Context, month int, beginTime, endTime int64) {
	orderQualityDaoInstance := dao.OrderQualityDao
	orderDaoInstance := orderDao.NewOrderDao("order")

	// 查找所有品控单
	filter := bson.M{
		"created_at": bson.M{
			"$gte": beginTime,
			"$lte": endTime,
		},
	}

	qualityOrders, err := orderQualityDaoInstance.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询品控单失败: %v", err.Error())
		return
	}

	updatedOrderCount := 0

	for index, qualityOrder := range qualityOrders {
		orderUpdated := false

		if qualityOrder.SkuIDCode != "" {
			continue
		}

		orderID := qualityOrder.OrderList[0].OrderID
		order, err := orderDaoInstance.Get(ctx, bson.M{"_id": orderID})
		if err != nil {
			zap.S().Errorf("查询订单 %s 失败: %v", orderID.Hex(), err)
			continue
		}

		// 检查品控单是否缺少sku_id_code
		if qualityOrder.SkuIDCode == "" || qualityOrder.SkuName == "" {
			// 查询商品信息

			skuIDCode := ""
			skuName := ""
			for _, v := range order.ProductList {
				if v.ProductID == qualityOrder.ProductID {
					skuIDCode = v.SkuIDCode
					skuName = v.SkuName
				}
			}

			qualityOrder.SkuIDCode = skuIDCode
			qualityOrder.SkuName = skuName
			orderUpdated = true
		}

		// 如果订单有更新，保存到数据库
		if orderUpdated {
			update := bson.M{
				"$set": bson.M{
					"sku_id_code": qualityOrder.SkuIDCode,
					"sku_name":    qualityOrder.SkuName,
				},
			}

			err = orderQualityDaoInstance.UpdateOne(ctx, bson.M{"_id": qualityOrder.ID}, update)
			if err != nil {
				zap.S().Errorf("更新品控单 %s 失败: %v", qualityOrder.ID.Hex(), err)
				continue
			}

			updatedOrderCount++
			zap.S().Infof("月份：%d，更新品控单，进度：%d/%d", month, index+1, len(qualityOrders))
		}

	}

	zap.S().Infof("成功更新 %d 个品控单，月份：%d", updatedOrderCount, month)
}

// 采购单，规格名称同步
func syncProductBuyPriceSkuName(ctx context.Context, month int, beginTime, endTime int64) {
	productBuyPriceDaoInstance := productBuyPriceDao.NewProductBuyPriceDao("product_buy_price")
	orderDaoInstance := orderDao.NewOrderDao("order")

	filter := bson.M{
		"created_at": bson.M{
			"$gte": beginTime,
			"$lte": endTime,
		},
	}

	productBuyPrices, err := productBuyPriceDaoInstance.Find(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询采购价数据失败: %v", err.Error())
		return
	}

	if len(productBuyPrices) == 0 {
		zap.S().Infof("没有采购价数据需要处理")
		return
	}

	zap.S().Infof("开始处理 %d 条采购价数据", len(productBuyPrices))

	// 处理每个采购价数据
	for index, buyPrice := range productBuyPrices {
		if buyPrice.SkuIDCode != "" {
			continue
		}

		orderID := buyPrice.OrderList[0].OrderID
		order, err := orderDaoInstance.Get(ctx, bson.M{"_id": orderID})
		if err != nil {
			zap.S().Errorf("查询订单 %s 失败: %v", orderID.Hex(), err)
			continue
		}

		skuIDCode := ""
		skuName := ""
		for _, v := range order.ProductList {
			if v.ProductID == buyPrice.ProductID {
				skuIDCode = v.SkuIDCode
				skuName = v.SkuName
				break
			}
		}

		// 更新采购价数据
		update := bson.M{
			"$set": bson.M{
				"sku_id_code": skuIDCode,
				"sku_name":    skuName,
			},
		}

		err = productBuyPriceDaoInstance.Update(ctx, bson.M{"_id": buyPrice.ID}, update)
		if err != nil {
			zap.S().Errorf("更新采购价 %s 失败: %v", buyPrice.ID.Hex(), err)
			continue
		}
		zap.S().Infof("月份：%d，更新采购价，进度：%d/%d", month, index+1, len(productBuyPrices))
	}

	zap.S().Infof("采购价规格名称同步完成，成功更新 %d 条，月份：%d", len(productBuyPrices), month)
}

// 删除商品审核记录
// func deleteProductAuditRecord(ctx context.Context) {
// 	productAuditDaoInstance := dao.ProductAuditDao

// 	filter := bson.M{
// 		"created_at": bson.M{
// 			"$gte": 1748707200000, // 2025-06-01 00:00:00
// 			"$lt":  1753932911000, // 2025-07-31 11:35:11
// 		},
// 	}

// 	err := productAuditDaoInstance.DeleteMany(ctx, filter)
// 	if err != nil {
// 		zap.S().Errorf("删除商品审核记录失败: %v", err.Error())
// 		return
// 	}

// 	zap.S().Infof("删除商品审核记录成功")
// }

// 交易快照
// func dealOrderSnapshot(ctx context.Context, month int, beginTime, endTime int64) {
// 	orderDaoInstance := orderDao.NewOrderDao("order")
// 	productImageDaoInstance := productImageDao.NewProductImageDao("product_image")

// 	// 查找所有订单
// 	filter := bson.M{
// 		"created_at": bson.M{
// 			"$gte": beginTime,
// 			"$lte": endTime,
// 		},
// 	}

// 	orders, err := orderDaoInstance.List(ctx, filter)
// 	if err != nil {
// 		zap.S().Errorf("查询订单失败: %v", err.Error())
// 		return
// 	}

// 	for index, order := range orders {

// 		for _, v := range order.ProductList {
// 			if v.ProductImageID != primitive.NilObjectID {
// 				productImage, err := productImageDaoInstance.Get(ctx, bson.M{"_id": v.ProductImageID})
// 				if err != nil && err != mongo.ErrNoDocuments {
// 					zap.S().Errorf("查询商品图片失败: %v", err.Error())
// 					continue
// 				}

// 				if productImage.ID == primitive.NilObjectID {
// 					continue
// 				}

// 				data := model.ProductImage{
// 					ID:        primitive.NewObjectID(),
// 					Product:   productImage.Product,
// 					OrderID:   order.ID,
// 					CreatedAt: order.CreatedAt,
// 				}
// 				productImageDaoInstance.Create(ctx, data)

// 			}
// 		}

// 		zap.S().Infof("月份：%d，更新订单，进度：%d/%d", month, index+1, len(orders))
// 	}
// }

// addDefaultSkuForProducts 为没有规格的商品新增默认规格
func addDefaultSkuForProducts(ctx context.Context) {
	productDaoInstance := productDao.NewProductDao("product")
	rdsInstance := global.RDBDefault
	_ = rdsInstance

	// 查找所有没有sku_list或sku_list为空的商品
	filter := bson.M{
		// "$or": []bson.M{
		// 	{"sku_list": bson.M{"$exists": false}},
		// 	{"sku_list": bson.M{"$size": 0}},
		// },
		// "deleted_at": 0,
		// "sale": true,
	}

	// 循环查询商品，每次查询50个
	page := int64(1)
	limit := int64(50)
	totalUpdatedCount := 0

	for {
		products, _, err := productDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询商品失败:%v", err.Error())
			return
		}

		// 如果没有更多商品，退出循环
		if len(products) == 0 {
			break
		}

		updatedCount := 0
		for _, product := range products {
			if len(product.SkuList) > 0 {
				continue
			}

			// 生成默认SKU
			idCode := util.NewUUIDNum()

			defaultSku := model.Sku{
				IDCode:                idCode,
				Name:                  "默认", // 使用商品名称作为SKU名称
				Price:                 product.Price,
				Cover:                 product.CoverImg.Name,
				RoughWeight:           product.Weight.RoughWeight,
				OutWeight:             product.Weight.OutWeight,
				NetWeight:             product.Weight.NetWeight,
				MarketWholesalePrice:  product.MarketWholesalePrice,
				EstimatePurchasePrice: product.EstimatePurchasePrice,
			}

			_ = defaultSku

			// 更新商品，添加默认SKU
			update := bson.M{
				"$set": bson.M{
					"start_price": product.Price,
					"sku_list":    []model.Sku{defaultSku},
				},
			}

			err = productDaoInstance.Update(ctx, bson.M{"_id": product.ID}, update)
			if err != nil {
				fmt.Printf("更新商品 %s 失败: %v\n", product.ID.Hex(), err)
				continue
			}
			redisKey := fmt.Sprintf("product:%s", product.ID.Hex())
			rdsInstance.Del(ctx, redisKey)

			updatedCount++
		}

		totalUpdatedCount += updatedCount
		zap.S().Infof("第 %d 页处理完成，更新了 %d 个商品，总计已更新 %d 个商品", page, updatedCount, totalUpdatedCount)

		page++
	}

	zap.S().Infof("成功为 %d 个商品添加默认规格", totalUpdatedCount)
}

// dealCartData 处理购物车数据
func dealCartData(ctx context.Context) {
	cartDaoInstance := cartDao.NewCartDao("cart")
	productDaoInstance := productDao.NewProductDao("product")

	// 查询所有购物车数据
	carts, err := cartDaoInstance.List(ctx, bson.M{})
	if err != nil {
		zap.S().Errorf("查询购物车数据失败:%v", err.Error())
		return
	}

	if len(carts) == 0 {
		zap.S().Infof("没有购物车数据需要处理")
		return
	}

	zap.S().Infof("开始处理 %d 条购物车数据", len(carts))

	// 收集所有商品ID
	productIDs := make([]primitive.ObjectID, 0)
	productIDMap := make(map[primitive.ObjectID]bool)
	for _, cart := range carts {
		if !productIDMap[cart.ProductID] {
			productIDs = append(productIDs, cart.ProductID)
			productIDMap[cart.ProductID] = true
		}
	}

	// 批量查询商品信息
	products, err := productDaoInstance.List(ctx, bson.M{
		"_id": bson.M{
			"$in": productIDs,
		},
	})
	if err != nil {
		zap.S().Errorf("查询商品信息失败:%v", err.Error())
		return
	}

	// 创建商品映射
	productMap := make(map[primitive.ObjectID]model.Product)
	for _, product := range products {
		productMap[product.ID] = product
	}

	updatedCount := 0
	errorCount := 0

	// 处理每个购物车项目
	for _, cart := range carts {
		product, exists := productMap[cart.ProductID]
		if !exists {
			zap.S().Warnf("购物车商品 %s 对应的商品信息不存在", cart.ProductID.Hex())
			errorCount++
			continue
		}

		// 检查购物车是否缺少SKU信息
		if cart.SkuIDCode == "" {
			// 如果商品有SKU列表，使用第一个SKU
			if len(product.SkuList) > 0 {
				sku := product.SkuList[0]

				// 更新购物车数据
				update := bson.M{
					"$set": bson.M{
						"sku_id_code": sku.IDCode,
					},
				}

				err = cartDaoInstance.Update(ctx, bson.M{"_id": cart.ID}, update)
				if err != nil {
					zap.S().Errorf("更新购物车 %s 失败: %v", cart.ID.Hex(), err)
					errorCount++
					continue
				}

				updatedCount++
				zap.S().Infof("更新购物车 %s，商品 %s，SKU: %s", cart.ID.Hex(), product.Title, sku.IDCode)
			} else {
				zap.S().Warnf("商品 %s 没有SKU信息，无法更新购物车 %s", product.Title, cart.ID.Hex())
				errorCount++
			}
		} else {
			// 验证SKU是否存在
			skuExists := false
			for _, sku := range product.SkuList {
				if sku.IDCode == cart.SkuIDCode {
					skuExists = true
					break
				}
			}

			if !skuExists {
				zap.S().Warnf("购物车 %s 的SKU %s 在商品 %s 中不存在", cart.ID.Hex(), cart.SkuIDCode, product.Title)
				errorCount++
			}
		}
	}

	zap.S().Infof("购物车数据处理完成，成功更新 %d 条，错误 %d 条", updatedCount, errorCount)
}

// CheckProductUpdatedAtDuplicates 检查现有商品中是否存在updated_at重复的情况
func CheckProductUpdatedAtDuplicates222(ctx context.Context) {
	productDaoInstance := productDao.NewProductDao("product")

	// 分页遍历查询，每次查询500条未删除的商品
	filter := bson.M{
		"deleted_at": 0,
	}
	page := int64(1)
	limit := int64(500)
	updatedAtCount := make(map[int64][]primitive.ObjectID)
	for {
		products, _, err := productDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询商品失败: %v", err.Error())
			return
		}
		if len(products) == 0 {
			break
		}
		for _, product := range products {
			updatedAtCount[product.UpdatedAt] = append(updatedAtCount[product.UpdatedAt], product.ID)
		}
		page++
	}

	// 检查重复
	duplicateCount := 0
	for updatedAt, productIDs := range updatedAtCount {
		if len(productIDs) > 1 {
			duplicateCount++
			zap.S().Warnf("发现updated_at重复: %d, 涉及商品数量: %d, 商品IDs: %v",
				updatedAt, len(productIDs), productIDs)
		}
	}

	if duplicateCount == 0 {
		zap.S().Infof("检查完成，未发现updated_at重复的商品")
	} else {
		zap.S().Warnf("检查完成，发现 %d 个重复的updated_at时间戳", duplicateCount)
	}
}

// CheckProductUpdatedAtDuplicates 检查现有商品中是否存在updated_at重复的情况
func CheckProductUpdatedAtDuplicates(ctx context.Context) {
	partDao := dao.IndexPartProductDao

	// 分页遍历查询，每次查询500条未删除的商品
	filter := bson.M{}
	list, err := partDao.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询商品失败: %v", err.Error())
		return
	}

	// m := make(map[primitive.ObjectID][]primitive.ObjectID)
	// for _, v := range list {
	// 	m[v.IndexPartID] = append(m[v.IndexPartID], v.ProductID)
	// }

	// for indexPartID, productIDs := range m {
	// 	zap.S().Infof("indexPartID: %s", indexPartID.Hex())

	// 	// 判断productIDs中是否有重复的商品ID
	// 	seen := make(map[primitive.ObjectID]bool)
	// 	duplicates := make([]primitive.ObjectID, 0)
	// 	for _, pid := range productIDs {
	// 		if seen[pid] {
	// 			duplicates = append(duplicates, pid)
	// 		} else {
	// 			seen[pid] = true
	// 		}
	// 	}
	// 	if len(duplicates) > 0 {
	// 		zap.S().Warnf("indexPartID: %s 存在重复的商品ID: %v", indexPartID.Hex(), duplicates)
	// 	}
	// }

	// 顺序重复
	m := make(map[primitive.ObjectID][]int)
	for _, v := range list {
		m[v.IndexPartID] = append(m[v.IndexPartID], v.Sort)
	}

	for indexPartID, productIDs := range m {
		zap.S().Infof("indexPartID: %s", indexPartID.Hex())

		// 判断productIDs中是否有重复的商品ID
		seen := make(map[int]bool)
		duplicates := make([]int, 0)
		for _, pid := range productIDs {
			if seen[pid] {
				duplicates = append(duplicates, pid)
			} else {
				seen[pid] = true
			}
		}
		if len(duplicates) > 0 {
			zap.S().Warnf("indexPartID: %s 存在重复的商品ID: %v", indexPartID.Hex(), duplicates)
		}
	}

}

// BatchUpdateSkuStock 批量更新商品SKU库存为99
func BatchUpdateSkuStock(ctx context.Context) {
	productDaoInstance := productDao.NewProductDao("product")
	rdsInstance := global.RDBDefault
	_ = rdsInstance

	filter := bson.M{}

	page := int64(1)
	limit := int64(500)
	totalUpdated := 0

	for {
		products, _, err := productDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询商品失败: %v", err.Error())
			return
		}
		if len(products) == 0 {
			break
		}

		updated := 0
		for _, product := range products {
			// 检查是否需要更新
			needUpdate := false
			updatedSkuList := make([]model.Sku, len(product.SkuList))
			copy(updatedSkuList, product.SkuList)

			for i, sku := range updatedSkuList {
				if sku.Stock != 99 {
					updatedSkuList[i].Stock = 99
					needUpdate = true
				}
			}

			if needUpdate {
				update := bson.M{
					"$set": bson.M{
						"sku_list": updatedSkuList,
					},
				}

				err := productDaoInstance.Update(ctx, bson.M{"_id": product.ID}, update)
				if err != nil {
					zap.S().Errorf("更新商品 %s SKU库存失败: %v", product.ID.Hex(), err)
					continue
				}

				// // 清除Redis缓存
				redisKey := fmt.Sprintf("product:%s", product.ID.Hex())
				rdsInstance.Del(ctx, redisKey)

				zap.S().Infof("商品:%s,标题：%s", product.ID.Hex(), product.Title)

				updated++
			}
		}

		totalUpdated += updated
		zap.S().Infof("第 %d 页处理完成，更新了 %d 个商品SKU库存，总计已更新 %d 个商品", page, updated, totalUpdated)
		page++
	}

	zap.S().Infof("批量更新SKU库存完成，总共更新了 %d 个商品", totalUpdated)
}

// checkRefundComplete 检查退款完结状态
func checkRefundComplete(ctx context.Context) {
	orderRefundDaoInstance := orderRefundDao.NewOrderRefundDao("order_refund")

	filter := bson.M{}

	page := int64(1)
	limit := int64(500)
	updated := 0

	for {
		refunds, _, err := orderRefundDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询商品失败: %v", err.Error())
			return
		}
		if len(refunds) == 0 {
			break
		}

		var ids []primitive.ObjectID
		for _, refund := range refunds {
			if !refund.IsComplete && refund.YeeRefundResult.Status == "SUCCESS" {
				ids = append(ids, refund.ID)
				updated++
			}
		}

		if len(ids) > 0 {
			err := orderRefundDaoInstance.UpdateMany(ctx, bson.M{"_id": bson.M{"$in": ids}},
				bson.M{"$set": bson.M{"is_complete": true}})
			if err != nil {
				zap.S().Errorf("更新退款 %s 状态失败: %v", ids, err)
				continue
			}

		}

		page++
	}

	zap.S().Infof("检查退款完结状态完成，总共更新了 %d 个退款", updated)
}

// batchSendDeliverFeeDetailGenerate 按月批量查询 parent_order 并发送配送费明细生成消息
func batchSendDeliverFeeDetailGenerate(ctx context.Context, monthTimestamp int64) {
	parentOrderDaoInstance := dao.ParentOrderDao

	monthStart, monthEnd, err := util.MonthScopeTimestamp(monthTimestamp)
	if err != nil {
		zap.S().Errorf("获取月份时间范围失败: %v", err)
		return
	}

	filter := bson.M{
		"created_at": bson.M{
			"$gte": monthStart,
			"$lt":  monthEnd,
		},
		"pay_status": model.PayStatusTypePaid,
	}

	page := int64(1)
	limit := int64(500)
	total := 0

	for {
		orders, _, err := parentOrderDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询parent_order失败: %v", err)
			break
		}
		if len(orders) == 0 {
			break
		}

		for _, order := range orders {
			if order.DeliverFeeRes.FinalDeliverFee == 0 || (order.DeliverType != model.DeliverTypeDoor && order.DeliverType != model.DeliverTypeInstantDeliver) {
				continue
			}

			if order.YeeRefundDeliverResult.Status == "SUCCESS" {
				continue
			}

			mnsSendService.NewMNSClient().SendDeliverFeeDetailGenerate(ctx, order.ID)
			total++
			zap.S().Infof("发送配送费明细生成消息，%v", total)
		}
		page++
	}

	zap.S().Infof("批量发送配送费明细生成消息完成，monthTimestamp: %d, 总共发送: %d", monthTimestamp, total)
}

func listSupplierInfo(ctx context.Context) {
	supplierDaoInstance := dao.SupplierDao
	yeeMerchantDaoInstance := dao.YeeMerchantDao

	// 查询所有可用供应商
	suppliers, err := supplierDaoInstance.List(ctx, bson.M{"account_status": 1})
	if err != nil {
		zap.S().Errorf("查询供应商失败: %v", err)
		return
	}

	for _, supplier := range suppliers {
		filter := bson.M{
			"object_type": model.ObjectTypeSupplier,
			"object_id":   supplier.ID,
		}
		yeeMerchant, err := yeeMerchantDaoInstance.Get(ctx, filter)
		if err != nil {
			zap.S().Errorf("查询供应商失败: %v", err)
			continue
		}

		// 打印供应商名称，yee的营业执照名称，法人
		zap.S().Infof("%s,  %s,  %s\n", supplier.ShopSimpleName, yeeMerchant.MerchantSubjectInfo.SignName, yeeMerchant.MerchantCorporationInfo.LegalName)
		time.Sleep(3 * time.Second)
	}
}

// queryUnavailableSuppliers 查询状态不可用的供应商
func queryUnavailableSuppliers(ctx context.Context) []model.Supplier {
	supplierDaoInstance := dao.SupplierDao

	// 查询状态不可用的供应商 (account_status != 1)
	filter := bson.M{
		"account_status": model.AccountStatusTypeForbid, // 禁用
		"deleted_at":     0,                             // 未删除
	}

	suppliers, err := supplierDaoInstance.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询不可用供应商失败: %v", err)
		return nil
	}

	return suppliers
}

// queryLatestOrdersBySupplierID 根据供应商ID查询最近的订单信息，按月分组统计
func queryLatestOrdersBySupplierID(ctx context.Context, supplier model.Supplier, file *os.File) int64 {
	orderDaoInstance := orderDao.NewOrderDao("order")
	orderRefundDaoInstance := dao.OrderRefundDao

	// 构建查询条件，查询所有该供应商的订单（不限制时间）
	filter := bson.M{
		"supplier_id":  supplier.ID,
		"pay_status":   model.PayStatusTypePaid,
		"order_status": model.OrderStatusTypeFinish,
		"deleted_at":   0,
		"created_at": bson.M{
			"$gte": *************, // 2023-06-18 00:00:00 保持原有的起始时间
		},
	}

	// 查询订单列表，只查询product_total_amount和created_at字段
	salesOpts := options.Find().SetProjection(bson.M{
		"product_total_amount": 1,
		"created_at":           1,
		"_id":                  1,
	})

	allOrders, err := orderDaoInstance.ListWithOption(ctx, filter, salesOpts)
	if err != nil {
		zap.S().Errorf("查询供应商订单失败: %v", err)
		return 0
	}

	// 按月分组订单
	monthlyStats := make(map[string]struct {
		Count       int64
		SalesAmount int64
		LastOrder   int64
		MonthStart  int64
		MonthEnd    int64
	})

	for _, order := range allOrders {
		// 使用订单的created_at获取月份范围
		monthStart, monthEnd, err := util.MonthScopeTimestamp(order.CreatedAt)
		if err != nil {
			zap.S().Errorf("获取订单月份范围失败: %v", err)
			continue
		}

		// 生成月份键值（格式：2025-01）
		monthKey := time.Unix(monthStart/1000, 0).Format("2006-01")

		// 更新月度统计
		stats := monthlyStats[monthKey]
		stats.Count++
		stats.SalesAmount += int64(order.ProductTotalAmount)
		stats.MonthStart = monthStart
		stats.MonthEnd = monthEnd

		// 更新最后下单时间
		if order.CreatedAt > stats.LastOrder {
			stats.LastOrder = order.CreatedAt
		}

		monthlyStats[monthKey] = stats
	}

	// 查询所有退款记录（不限制时间）
	refundFilter := bson.M{
		"supplier_id": supplier.ID,
		"deleted_at":  0,
		"created_at": bson.M{
			"$gte": *************, // 2023-06-18 00:00:00
		},
	}

	allRefunds, err := orderRefundDaoInstance.List(ctx, refundFilter)
	if err != nil {
		zap.S().Errorf("查询供应商退款记录失败: %v", err)
		return 0
	}

	// 按月分组退款
	monthlyRefunds := make(map[string]int64)
	for _, refund := range allRefunds {
		if refund.AuditStatus == model.AuditStatusTypePass {
			// 使用退款的created_at获取月份范围
			monthStart, _, err := util.MonthScopeTimestamp(refund.CreatedAt)
			if err != nil {
				zap.S().Errorf("获取退款月份范围失败: %v", err)
				continue
			}

			monthKey := time.Unix(monthStart/1000, 0).Format("2006-01")
			monthlyRefunds[monthKey] += int64(refund.AuditAmount)
		}
	}

	// 输出按月统计信息到文件
	if file != nil {
		file.WriteString(fmt.Sprintf("=== 供应商: %s (ID: %s) 按月统计 ===\n", supplier.ShopSimpleName, supplier.ID.Hex()))
	}

	var totalCount int64 = 0
	var totalSalesAmount int64 = 0
	var totalRefundAmount int64 = 0
	var lastOrderTime int64 = 0

	// 按月份排序输出
	var monthKeys []string
	for monthKey := range monthlyStats {
		monthKeys = append(monthKeys, monthKey)
	}

	// 简单排序
	for i := 0; i < len(monthKeys); i++ {
		for j := i + 1; j < len(monthKeys); j++ {
			if monthKeys[i] > monthKeys[j] {
				monthKeys[i], monthKeys[j] = monthKeys[j], monthKeys[i]
			}
		}
	}

	for _, monthKey := range monthKeys {
		stats := monthlyStats[monthKey]
		refundAmount := monthlyRefunds[monthKey]
		netAmount := stats.SalesAmount - refundAmount

		// 累计总数
		totalCount += stats.Count
		totalSalesAmount += stats.SalesAmount
		totalRefundAmount += refundAmount

		// 更新最后下单时间
		if stats.LastOrder > lastOrderTime {
			lastOrderTime = stats.LastOrder
		}

		// 格式化最后下单时间
		var lastOrderTimeStr string
		if stats.LastOrder > 0 {
			lastOrderTimeStr = time.Unix(stats.LastOrder/1000, 0).Format("2006-01-02 15:04:05")
		} else {
			lastOrderTimeStr = "无订单"
		}

		// 输出月度统计到文件
		monthOutput := fmt.Sprintf("  %s月: 订单数: %d, 销售额: %.2f 元, 退款: %.2f 元, 净额: %.2f 元, 最后下单: %s\n",
			monthKey,
			stats.Count,
			float64(stats.SalesAmount)/100,
			float64(refundAmount)/100,
			float64(netAmount)/100,
			lastOrderTimeStr)

		if file != nil {
			file.WriteString(monthOutput)
		}

		// 输出到日志
		zap.S().Infof("供应商: %s, %s月: 订单: %d, 销售额: %.2f 元, 退款: %.2f 元, 净额: %.2f 元",
			supplier.ShopSimpleName, monthKey, stats.Count,
			float64(stats.SalesAmount)/100,
			float64(refundAmount)/100,
			float64(netAmount)/100)
	}

	// 输出总计信息
	var totalLastOrderTimeStr string
	if lastOrderTime > 0 {
		totalLastOrderTimeStr = time.Unix(lastOrderTime/1000, 0).Format("2006-01-02 15:04:05")
	} else {
		totalLastOrderTimeStr = "无订单"
	}

	totalOutput := fmt.Sprintf("  总计: 订单数: %d, 销售额: %.2f 元, 退款: %.2f 元, 净额: %.2f 元, 最后下单: %s\n\n",
		totalCount,
		float64(totalSalesAmount)/100,
		float64(totalRefundAmount)/100,
		float64(totalSalesAmount-totalRefundAmount)/100,
		totalLastOrderTimeStr)

	if file != nil {
		file.WriteString(totalOutput)
	}

	return totalCount
}

// updateProductTagTimeFields 为现有商品标签添加时间字段
func updateProductTagTimeFields(ctx context.Context) {
	productTagDaoInstance := dao.ProductTagDao
	rdsInstance := global.RDBDefault

	// 查找所有商品标签
	filter := bson.M{}

	totalUpdated := 0
	totalProcessed := 0

	zap.S().Info("开始为商品标签添加时间字段...")

	// 查询所有商品标签
	tags, err := productTagDaoInstance.List(ctx, filter)
	if err != nil {
		zap.S().Errorf("查询商品标签失败: %v", err.Error())
		return
	}

	zap.S().Infof("找到 %d 个商品标签", len(tags))

	for _, tag := range tags {
		totalProcessed++

		// 检查是否需要更新时间字段
		needUpdate := false
		updateFields := bson.M{}

		now := time.Now().UnixMilli()

		// 如果CreatedAt为空或为0，设置为当前时间
		if tag.CreatedAt == 0 {
			updateFields["created_at"] = now
			needUpdate = true
		}

		// 如果UpdatedAt为空或为0，设置为当前时间
		if tag.UpdatedAt == 0 {
			updateFields["updated_at"] = now
			needUpdate = true
		}

		// DeletedAt字段如果不存在，设置为0（未删除状态）
		// 注意：这里我们总是设置DeletedAt为0，因为现有数据可能没有这个字段
		updateFields["deleted_at"] = 0
		needUpdate = true

		if needUpdate {
			// 更新数据库
			updateQuery := bson.M{
				"$set": updateFields,
			}

			err := productTagDaoInstance.Update(ctx, bson.M{"_id": tag.ID}, updateQuery)
			if err != nil {
				zap.S().Errorf("更新商品标签 %s 时间字段失败: %v", tag.ID.Hex(), err)
				continue
			}

			// 清除Redis缓存
			redisKey := fmt.Sprintf("productTag:%s", tag.ID.Hex())
			rdsInstance.Del(ctx, redisKey)

			totalUpdated++
			zap.S().Infof("更新商品标签: ID=%s, 标题=%s, 类型=%d",
				tag.ID.Hex(), tag.Title, tag.TagType)
		}
	}

	zap.S().Infof("商品标签时间字段更新完成！总处理: %d, 总更新: %d", totalProcessed, totalUpdated)
}

// resetSupplierFrozenBalanceAmount 重置供应商冻结金额为0并删除缓存
func resetSupplierFrozenBalanceAmount(ctx context.Context) {
	supplierDaoInstance := dao.SupplierDao
	rdsInstance := global.RDBDefault

	filter := bson.M{}

	page := int64(1)
	limit := int64(100)
	totalUpdated := 0

	for {
		suppliers, _, err := supplierDaoInstance.ListByPage(ctx, filter, page, limit)
		if err != nil {
			zap.S().Errorf("查询供应商失败: %v", err.Error())
			return
		}
		if len(suppliers) == 0 {
			break
		}

		updated := 0
		for _, supplier := range suppliers {
			// 更新冻结金额为0
			update := bson.M{
				"$set": bson.M{
					"frozen_balance_amount": 0,
				},
			}

			err := supplierDaoInstance.Update(ctx, bson.M{"_id": supplier.ID}, update)
			if err != nil {
				zap.S().Errorf("更新供应商 %s 冻结金额失败: %v", supplier.ID.Hex(), err)
				continue
			}

			// 删除供应商缓存
			redisKey := fmt.Sprintf("supplier:%s", supplier.ID.Hex())
			rdsInstance.Del(ctx, redisKey)

			updated++
			zap.S().Infof("重置供应商冻结金额: ID=%s, 店铺名=%s, 原冻结金额=%.2f元",
				supplier.ID.Hex(), supplier.ShopSimpleName, float64(supplier.FrozenBalanceAmount)/100)
		}

		totalUpdated += updated
		zap.S().Infof("第 %d 页处理完成，更新了 %d 个供应商，总计已更新 %d 个供应商", page, updated, totalUpdated)
		page++
	}

	zap.S().Infof("供应商冻结金额重置完成，总共更新了 %d 个供应商", totalUpdated)
}
