package mnsSendService

import (
	"base/model"
)

// SendCouponUserRefresh 刷新优惠券状态
func (s *MnsClient) SendCouponUserRefresh(couponUserID string, delaySecond int64) {
	content := encodeContentStruct(model.MNSCouponUser{
		CouponUserID: couponUserID,
	})

	msg := model.MNSCouponUserRefresh + "@" + content

	s.send(msg, delaySecond)
}

// SendCouponUserUsed 已使用优惠券
func (s *MnsClient) SendCouponUserUsed(couponUserID string, parentOrderID string, delaySecond int64) {
	content := encodeContentStruct(model.MNSCouponUser{
		CouponUserID:  couponUserID,
		ParentOrderID: parentOrderID,
	})

	msg := model.MNSCouponUserUsed + "@" + content

	s.send(msg, delaySecond)
}

// SendCouponUserRestore 恢复优惠券
func (s *MnsClient) SendCouponUserRestore(couponUserID string, delaySecond int64) {
	// 因取消订单恢复优惠券
	content := encodeContentStruct(model.MNSCouponUser{
		CouponUserID: couponUserID,
	})

	msg := model.MNSCouponUserRestore + "@" + content

	s.send(msg, delaySecond)
}

// SendCouponStockRefresh 刷新优惠券批次
func (s *MnsClient) SendCouponStockRefresh(couponStockID string, delaySecond int64) {
	content := encodeContentStruct(model.MNSCouponStock{
		CouponStockID: couponStockID,
	})

	msg := model.MNSCouponStockRefresh + "@" + content

	s.send(msg, delaySecond)
}

// SendCheckPromotionSubsidy 检查营销补贴
func (s *MnsClient) SendCheckPromotionSubsidy(orderID string, delaySecond int64) {
	content := encodeContentStruct(model.MNSPromotionSubsidy{
		OrderID: orderID,
	})

	msg := model.MNSCheckPromotionSubsidy + "@" + content

	s.send(msg, delaySecond)
}
