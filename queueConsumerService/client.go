package queueConsumerService

import (
	"base/global"
	"base/model"
	"base/service/buyerBalanceRecordService"
	"base/service/orderAgentPayService"
	"base/util"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	_ "net/http/pprof"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

func CheckQueue(ctx context.Context) {
	//zap.S().Infof("%s", "执行 CheckQueue")
	milli := time.Now().UnixMilli()

	rdb := global.RDBDefault

	key := global.QueueName

	val := rdb.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min: "0",
		Max: fmt.Sprintf("%d", milli),
	}).Val()

	for _, s := range val {
		dealMessage(ctx, s)
		rdb.ZRem(ctx, key, s)
	}

}

func dealMessage(ctx context.Context, msg string) error {
	queueMessage, err := decodeMessage(msg)
	if err != nil {
		return err
	}

	switch queueMessage.Event {
	case model.QueueEventDivideNormalOrder:
		id, err := util.ConvertToObjectWithCtx(ctx, queueMessage.Value)
		if err != nil {
			return err
		}
		err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayNormalOrder(ctx, id)
		if err != nil {
			return err
		}
		return nil
	case model.QueueEventDivideDebtOrder:
		id, err := util.ConvertToObjectWithCtx(ctx, queueMessage.Value)
		if err != nil {
			return err
		}
		err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayDebtOrder(ctx, id)
		if err != nil {
			return err
		}
		return nil

	case model.QueueEventDivideDeliver:
		// 配送费分账
		id, err := util.ConvertToObjectWithCtx(ctx, queueMessage.Value)
		if err != nil {
			return err
		}
		err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayDeliver(ctx, id)
		if err != nil {
			return err
		}
		return nil

	case model.QueueEventBalanceCreateRecord:
		// 补差
		var d model.QueueValueBalanceRecord
		err := decodeValue(queueMessage.Value, &d)
		if err != nil {
			return err
		}

		buyerID, err := util.ConvertToObjectWithCtx(ctx, d.BuyerID)
		if err != nil {
			return err
		}

		objectID, err := util.ConvertToObjectWithCtx(ctx, d.ObjectID)
		if err != nil {
			return err
		}

		err = buyerBalanceRecordService.NewBuyerBalanceRecordService().CreateRecord(context.Background(), d.BuyerBalanceRecordType, buyerID, objectID, d.Amount)
		if err != nil {
			return err
		}
		return nil
	}
	zap.S().Infof("no message event: %s", queueMessage.Event)
	return nil
}

func decodeMessage(msg string) (model.QueueMessage, error) {
	decodeString, err := base64.StdEncoding.DecodeString(msg)
	if err != nil {
		return model.QueueMessage{}, err
	}

	var data model.QueueMessage
	err = json.Unmarshal(decodeString, &data)
	if err != nil {
		return model.QueueMessage{}, err
	}

	zap.S().Infof("消息ID:%s\",消息内容:%s", data.MessageID, string(decodeString))

	return data, nil
}

func decodeValue(content string, data any) error {
	decodeString, err := base64.StdEncoding.DecodeString(content)
	if err != nil {
		zap.S().Errorf("decodeContent,内容:%s，err:%v", content, err)
		return err
	}
	err = json.Unmarshal(decodeString, &data)
	if err != nil {
		return err
	}
	return nil
}
